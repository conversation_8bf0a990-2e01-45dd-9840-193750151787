{"APP_SECRET_KEY": "GqQbhY22H7", "APP_LANGUAGE_CODE": "en-us.utf-8", "APP_TIME_ZONE": "America/Bogota", "APP_NAMESPACE": "", "APP_MODULES": ["core_config", "customers", "integrations", "inventory", "logistics", "operations", "rfid", "rfid_config", "stock"], "AWS_SECRET_ACCESS_KEY": "", "AWS_ACCESS_KEY_ID": "", "AWS_REGION": "us-east-1", "AWS_STORAGE_BUCKET_NAME": "", "DB_HOST": "localhost", "DB_PORT": 5432, "DB_NAME": "kong_rfid_db_2", "DB_USER": "kong_user", "DB_PASSWORD": "kong_password", "BROKER_URL": "None", "RABBIT_HOST": "", "RABBIT_PORT": 5672, "RABBIT_DEFAULT_USER": "", "RABBIT_DEFAULT_PASS": "", "RABBIT_ENV_VHOST": "kong", "CELERY_ALWAYS_EAGER": "True", "CUSTOMER_COMPANY_PREFIX": "None", "APP_COMPANY_PREFIX_METADATA": "", "AWS_LAMBDA_NAME": "", "AWS_SQS_URL": "", "SENTRY_DSN": "", "AUTO_TRACKING": "True", "AWS_SQS_INTEGRATION_URL": ""}